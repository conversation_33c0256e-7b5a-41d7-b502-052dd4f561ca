<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="System" pageSubtitle="Transactions" />

    <!-- Filters Section -->
    <div class="px-4 mb-5">
      <div class="bg-white rounded-md shadow-sm mb-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
        </div>

        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            <!-- Transaction Identification Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Identification</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Transaction ID</label>
                  <input type="text" placeholder="Enter transaction ID"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.transaction_id"
                         @keyup.enter="applyFilters()">
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Reference ID</label>
                  <input type="text" placeholder="Enter reference ID"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.reference_id"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Customer Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Customer</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Phone Number</label>
                  <input type="text" placeholder="254XXXXXXXXX"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.mobile_number"
                         @keyup.enter="applyFilters()">
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Profile ID</label>
                  <input type="text" placeholder="Enter profile ID"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.profile_id"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Transaction Details Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Details</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Transaction Type</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="selectedTransactionType"
                    @change="onTransactionTypeChange">
                    <option value="">All Types</option>
                    <option value="1">Credit</option>
                    <option value="2">Debit</option>
                    <option value="7">Refund</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Source</label>
                  <input type="text" placeholder="Enter source"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.source"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Date Range Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                  <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @update:model-value="selectDate"
                  />
                </div>
                <div class="flex justify-end pt-2">
                  <button @click="applyFilters()"
                          class="px-3 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700 transition-colors">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mx-3">
      <auto-table
        :headers="tableHeaders"
        :data="transactions"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="gotToPage"
      >
        <!-- Index Column -->
        <template #index="{ item, index }">
          <span>{{ (index + 1) + ((offset - 1) * limit) }}</span>
        </template>

        <!-- Transaction Type Column -->
        <template #transaction_type_id="{ item }">
          <span
            class="inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md transform transition-transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg w-20 text-center"
            :class="getTransactionTypeClass(parseInt(item.transaction_type_id))"
          >
            {{ getTransactionTypeText(parseInt(item.transaction_type_id)) }}
          </span>
        </template>

        <!-- Customer Column -->
        <template #msisdn="{ item }">
          <span>+{{ item.msisdn }}</span>
        </template>

        <!-- Amount Column -->
        <template #amount="{ item }">
          <span class="font-medium">{{ item.currency }}. {{ parseFloat(item.amount).toFixed(2) }}</span>
        </template>

        <!-- Game Name Column -->
        <template #game_name="{ item }">
          <span class="text-sm font-medium text-black-600">{{ getGameName(item).split(':')[1] }}</span>
        </template>

        <!-- Description Column -->
        <template #description="{ item }">
          <span style="font-size: 12px; color: grey"> {{ item.description }}</span>
        </template>

        <!-- Source Column -->
        <template #source="{ item }">
          <span>{{ item.source }}</span>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
        </template>
      </auto-table>
    </div>

  </div>
</template>

<script>

import moment from "moment";
// import numeral from "numeral"
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      selectedTransactionType: '',
      //
      transactions: [],
      //
      tableHeaders: [
        { key: 'index', label: '#', align: 'center' },
        { key: 'transaction_type_id', label: 'Trxn Type', align: 'center' },
        { key: 'msisdn', label: 'Customer', align: 'left' },
        { key: 'amount', label: 'Amount', align: 'left' },
        { key: 'game_name', label: 'Game Name', align: 'left' },
        { key: 'description', label: 'Description', align: 'left' },
        { key: 'source', label: 'Source', align: 'left' },
        { key: 'created_at', label: 'Date', align: 'center' }
      ],
      moreParams: {
        mobile_number: '',
        profile_id: '',
        transaction_id: '',
        reference_id: '',
        amount: '',
        source: '',
        transaction_type_id: '',
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

    }
  },
  mounted() {
    this.setTransactions()
  },
  methods: {
    ...mapActions(["getTransactions", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    applyFilters() {
      let vm = this
      vm.isLoading = true;

      // Set transaction type ID from dropdown selection
      vm.moreParams.transaction_type_id = vm.selectedTransactionType;
      vm.moreParams.timestamp = Date.now();

      vm.setTransactions();
    },

    onTransactionTypeChange() {
      this.moreParams.transaction_type_id = this.selectedTransactionType;
    },

    async selectDate() {
      let vm = this

      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        vm.moreParams.start = '';
        vm.moreParams.end = '';
        vm.moreParams.timestamp = Date.now();
        await vm.setTransactions();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      vm.moreParams.start = vm.formatDate(this.date[0]);
      vm.moreParams.end = vm.formatDate(this.date[1]);
      vm.moreParams.timestamp = Date.now();

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await vm.setTransactions();
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setTransactions()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    async setTransactions() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.moreParams) {
        if (app.moreParams.hasOwnProperty(key) && app.moreParams[key] !== '') {
          params.append(key, app.moreParams[key]);
        }
      }

      const queryString = params.toString();

      console.log("Params: " + queryString);

      let response = await this.getTransactions(queryString)

      if (response.status === 200) {
        app.transactions = response.message.result
        app.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.transactions.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.$swal.fire({
          title: 'Error!',
          text: response.message || 'Failed to load transactions',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }

      app.isLoading = false
    },

    getTransactionTypeClass(typeId) {
      const typeMap = {
        1: 'bg-gradient-to-r from-green-500 to-green-600', // Credit
        2: 'bg-gradient-to-r from-orange-400 to-orange-500', // Debit
        7: 'bg-gradient-to-r from-purple-500 to-purple-600', // Refund
      };

      return typeMap[typeId] || 'bg-gradient-to-r from-gray-400 to-gray-500';
    },

    getTransactionTypeText(typeId) {
      const typeMap = {
        1: 'Credit',
        2: 'Debit',
        7: 'Refund',
      };

      return typeMap[typeId] || 'Unknown';
    },

    getGameName(item) {
      try {
        // Check if extra_data exists and is not empty
        if (!item.extra_data) {
          return '-';
        }

        // Parse the JSON string
        const extraData = JSON.parse(item.extra_data);

        // Extract i_gamedesc
        if (extraData.i_gamedesc) {
          return extraData.i_gamedesc;
        }

        return '-';
      } catch (error) {
        // If JSON parsing fails or any other error occurs
        console.error('Error parsing extra_data:', error);
        return '-';
      }
    },
  },
}
</script>

<style scoped>
.filter-card {
  background-color: white;
  transition: all 0.2s ease;
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

:deep(.dp__input) {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}
</style>
