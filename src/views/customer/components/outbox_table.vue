<template>
  <div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <div class="mx-3">
      <auto-table
        :headers="tableHeaders"
        :data="data"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :has-actions="true"
        :get-actions="getRowActions"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- Customer Column -->
        <template #msisdn="{ item }">
          <span
            @click="copyToClipboard(formatPhoneNumber(item.msisdn))"
            title="Click to copy"
            class="cursor-pointer hover:text-blue-600"
          >
            {{ formatPhoneNumber(item.msisdn) }}
          </span>
        </template>

      <!-- Message Column -->
      <template #message="{ item }">
        <div class="max-w-md">
          <p class="whitespace-pre-line text-sm message-text">{{ item.message }}</p>
        </div>
      </template>
 
      <!-- Message Type Column -->
      <template #message_type="{ item }">
        <div class="badge-container">
          <span 
            class="badge bet-type-badge" 
            :class="getMessageTypeClass(item.message_type)"
            @click="copyToClipboard(item.message_type)"
            title="Click to copy"
          >
            {{ item.message_type }}
          </span>
        </div>
      </template>

      <!-- Sender ID Column -->
      <template #sender_id="{ item }">
        <div class="badge-container">
          <span 
            class="badge source-badge"
            @click="copyToClipboard(item.sender_id)"
            title="Click to copy"
          >
            {{ item.sender_id }}
          </span>
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div class="badge-container">
          <span 
            class="badge date-badge"
            @click="copyToClipboard(moment(item.created_at).format('llll'))"
            title="Click to copy"
          >
            {{ moment(item.created_at).format('llll') }}
          </span>
        </div>
      </template>

      <!-- DLR State Column -->
      <template #dlr_state="{ item }">
        <div class="badge-container">
          <span 
            class="badge status-badge" 
            :class="getStatusClass(item.dlr_state)"
            @click="copyToClipboard(getStatusText(item.dlr_state))"
            title="Click to copy"
          >
            {{ getStatusText(item.dlr_state) }}
          </span>
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="badge-container">
          <action-dropdown 
            :show-text="false"
            button-class="z-20"
            menu-class="z-30 right-0 origin-top-right"
            :menu-width="48"
          >
            <action-item
              v-if="parseInt(item.dlr_state) !== 1"
              text="Repost"
              color="blue"
              @click="repostMessage(item.id)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </template>
            </action-item>
            
            <action-item
              text="Copy Message"
              color="green"
              @click="copyMessage(item.message)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </template>
            </action-item>
          </action-dropdown>
        
        </div>
        </template>
      </auto-table>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import { AutoTable, CustomLoading, ActionDropdown, ActionItem } from '@/components/common';

export default {
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      phone: null,
      data: [],
      tableHeaders: [
        { key: 'msisdn', label: 'Customer' },
        { key: 'message', label: 'Message' },
        { key: 'message_type', label: 'Type', align: 'center' },
        { key: 'sender_id', label: 'Sender ID', align: 'center' },
        { key: 'created_at', label: 'Date' },
        { key: 'dlr_state', label: 'Status', align: 'center' },
        // { key: 'actions', label: 'Action', sortable: false, align: 'center' }
      ],
      moreParams: {
        dlr_status: '',
        mobile_number: '',
        start: '',
        end: '',
        page: '',
        limit: 10,
        timestamp: 'timestamp',
        skip_cache: '0',
        export: '',
      },
    }
  },
  components: {
    AutoTable,
    CustomLoading,
    ActionDropdown,
    ActionItem
  },

  methods: {
    ...mapActions(["getOutbox", "toggleSideMenu",]),

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [];

      if (parseInt(item.dlr_state) !== 1) {
        actions.push({
          label: 'Repost',
          action: () => this.repostMessage(item.id),
          icon: 'fas fa-redo'
        });
      }

      actions.push({
        label: 'Copy Message',
        action: () => this.copyMessage(item.message),
        icon: 'fas fa-copy'
      });

      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setOutbox(this.phone);
    },

    formatPhoneNumber(phone) {
      // Format phone number for better readability
      if (!phone) return '';
      return phone.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, '$1 $2 $3 $4');
    },
    
    getMessageTypeClass(type) {
      const typeMap = {
        'NOTIFICATIONS': 'bg-blue-100 text-blue-800',
        'REGISTER': 'bg-green-100 text-green-800',
        'MARKETING': 'bg-purple-100 text-purple-800',
        'TRANSACTIONAL': 'bg-orange-100 text-orange-800'
      };
      
      return typeMap[type] || 'bg-gray-100 text-gray-800';
    },
    
    getStatusClass(status) {
      status = parseInt(status);
      switch (status) {
        case 1:
          return 'status-sent';
        case 0:
          return 'status-pending';
        case 3:
          return 'status-failed';
        default:
          return 'status-default';
      }
    },
    
    getStatusText(status) {
      status = parseInt(status);
      switch (status) {
        case 1:
          return 'Sent';
        case 0:
          return 'Pending';
        case 3:
          return 'Failed';
        default:
          return 'Unknown';
      }
    },
    
    copyMessage(message) {
      navigator.clipboard.writeText(message)
        .then(() => {
          // You could add a toast notification here
          console.log('Message copied to clipboard');
        })
        .catch(err => {
          console.error('Failed to copy message: ', err);
        });
    },

    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setOutbox(this.phone)
    },

    repostMessage(id) {
      console.log('Reposting message with ID:', id);
      // Implement repost functionality here
    },

    async setOutbox(num) {
      let app=this
      this.isLoading = true
      app.moreParams.mobile_number = num
      app.phone = num

      console.log("Outbox OK num>>>: " + JSON.stringify(num))
      console.log("Outbox OK moreParams>>>: " + JSON.stringify(app.moreParams))

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      let response = await this.getOutbox(queryString)

      // console.log("data OK: " + JSON.stringify(response.message.result))
      this.data = response.message.result ?? []
      this.total = parseInt(response.message.record_count)

      this.showDropdown = []
      for (let i = 0; i < this.data.length; i++) {
        this.showDropdown.push(false)
      }

      this.isLoading = false
    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          // You could add a toast notification here if you have one
          console.log('Copied to clipboard:', text);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
        });
    },
  },
}
</script>

<style scoped>
.whitespace-pre-line {
  white-space: pre-line;
}

/* Badge container to ensure consistent alignment */
.badge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
}

/* Base badge styles */
.customer-badge,
.message-type-badge,
.sender-badge,
.date-badge,
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 100px;
  letter-spacing: 0.3px;
}

/* Customer badge */
.customer-badge {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
  font-weight: bold;
  min-width: 150px;
}

/* Message text */
.message-text {
  max-height: 60px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

/* Sender badge */
.sender-badge {
  background-color: #e9ecef;
  color: #495057;
  border: 1px solid #ced4da;
}

/* Date badge */
.date-badge {
  background-color: #f1f3f5;
  color: #495057;
  font-size: 11px;
  min-width: 180px;
}

/* Status badges */
.status-sent {
  background-color: #2ecc71; /* Green */
  color: white;
}

.status-pending {
  background-color: #f39c12; /* Orange */
  color: white;
}

.status-failed {
  background-color: #e74c3c; /* Red */
  color: white;
}

.status-default {
  background-color: #95a5a6; /* Gray */
  color: white;
}

/* Hover effects */
.customer-badge:hover,
.message-type-badge:hover,
.sender-badge:hover,
.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}
</style>
