<template>
  <div class="min-h-screen bg-gray-50">
    <page-header pageName="Virtual Bets" pageSubtitle="Summary" />
    
    <div class="p-2 sm:p-4">
      <!-- Filters Section -->
      <div class="filter-card p-2 sm:p-3 mb-4 border rounded-md hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-3">Filters</h4>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <!-- Provider Filter -->
          <div>
            <label class="block text-xs text-gray-600 mb-1">Provider</label>
            <select
              v-model="filters.provider_name"
              @change="applyFilters"
              class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">All Providers</option>
              <option v-for="provider in apiProviders" :key="provider" :value="provider">
                {{ provider.value }}
              </option>
            </select>
          </div>
          
          <!-- Date Range Filter -->
          <div class="col-span-2">
            <label class="block text-xs text-gray-600 mb-1">Date Range</label>
            <div class="grid grid-cols-2 gap-4">
              <!-- Start Date -->
              <div>
                <label class="block text-xs text-gray-500 mb-1">From</label>
                <div class="flex gap-1">
                  <select
                    v-model="startMonth"
                    @change="handleDateChange"
                    class="flex-1 px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option v-for="month in months" :key="month.value" :value="month.value">
                      {{ month.label }}
                    </option>
                  </select>
                  <select
                    v-model="startYear"
                    @change="handleDateChange"
                    class="flex-1 px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option v-for="year in years" :key="year" :value="year">
                      {{ year }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- End Date -->
              <div>
                <label class="block text-xs text-gray-500 mb-1">To</label>
                <div class="flex gap-1">
                  <select
                    v-model="endMonth"
                    @change="handleDateChange"
                    class="flex-1 px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option v-for="month in months" :key="month.value" :value="month.value">
                      {{ month.label }}
                    </option>
                  </select>
                  <select
                    v-model="endYear"
                    @change="handleDateChange"
                    class="flex-1 px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option v-for="year in years" :key="year" :value="year">
                      {{ year }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <!-- Clear Date Range Button -->
            <div class="mt-2">
              <button
                @click="clearDateRange"
                class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-md transition-colors"
              >
                Clear Range
              </button>
            </div>
          </div>



        </div>
      </div>

      <!-- Games Summary Table -->
      <div class="bg-white rounded-lg shadow">
        <auto-table
          :items="summary"
          :loading="isLoading"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="false"
          :pagination="total > limit"
          :show-items-count="true"
          :decimal-places="decimalPlaces"
          empty-message="No games summary data available"
          @page-change="handlePageChange"
        >
        </auto-table>
      </div>
    </div>

    <!-- Loading Overlay -->
    <CustomLoading v-if="isLoading" />
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { AutoTable, CustomLoading, } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  name: "VirtualBetsSummary",
  components: {
    AutoTable,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      windowWidth: window.innerWidth,
      searchTimeout: null, // For debouncing search input
      filters: {
        provider_name: {},
      },
      apiProviders:[],
      summary: [],
      params: {
        start: '',
        end: '',
        limit: 100,
        timestamp: Date.now()
      },
      limit: 100,
      offset: 1,
      total: 0,

      // Date range picker data
      startMonth: String(new Date().getMonth() + 1).padStart(2, '0'),
      startYear: new Date().getFullYear() >= 2025 ? new Date().getFullYear() : 2025,
      endMonth: String(new Date().getMonth() + 1).padStart(2, '0'),
      endYear: new Date().getFullYear() >= 2025 ? new Date().getFullYear() : 2025,
      months: [
        { label: 'January', value: '01' },
        { label: 'February', value: '02' },
        { label: 'March', value: '03' },
        { label: 'April', value: '04' },
        { label: 'May', value: '05' },
        { label: 'June', value: '06' },
        { label: 'July', value: '07' },
        { label: 'August', value: '08' },
        { label: 'September', value: '09' },
        { label: 'October', value: '10' },
        { label: 'November', value: '11' },
        { label: 'December', value: '12' }
      ],
      years: [2025, 2026, 2027, 2028, 2029],

      decimalPlaces:{
        Total_stake:2,
        Cashbet_stake:2,
        bonus_bet:2,
        Bonus_stake:2,
        Free_stake:2,
        Total_bets:2,
        GGR:2
      }
    }
  },
  created() {
    // Set initial date range values in yyyy/mm format
    this.params.start = `${this.startYear}/${this.startMonth}`;
    this.params.end = `${this.endYear}/${this.endMonth}`;
    this.fetchVirtualBetsSummary();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    // Clear any pending search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    },
    uniqueGames() {
      const games = [...new Set(this.gamesSummary.map(item => item.created_by))];
      return games.filter(game => game).sort();
    },
    uniqueProviders() {
      const providers = [...new Set(this.gamesSummary.map(item => item.provider_name))];
      return providers.filter(provider => provider).sort();
    },
    
  },
  methods: {
    ...mapActions(["getVirtualBetsSummary"]),

    handleResize() {
      this.windowWidth = window.innerWidth;
    },
    

    // Fetch virtual bets summary data with server-side filtering
    async fetchVirtualBetsSummary() {
      this.isLoading = true;

      // Ensure required parameters are always present
      const apiParams = {
        provider_name:this.filters.provider_name.key,
        start: this.params.start,
        end: this.params.end,
        timestamp: Date.now(),
        limit: this.limit || 100,
      };

      try {
        const response = await this.getVirtualBetsSummary(apiParams);

        if (response && response.status === 200) {
          this.summary = response.message.result || [];
          
          // 
          let providers = response.message.providers || [];
          const apiProviders = [];

          // Create key-value pairs from provider names
          providers.forEach(provider => {
            const key = provider.provider_name.toLowerCase().replace(/\s+/g, '_');
            const value = provider.provider_name;
            apiProviders.push({ key, value });
          });

          this.apiProviders = apiProviders;

          this.total = response.message.record_count || this.summary.length;
        } else {
          this.summary = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('Error fetching virtual bets summary:', error);
        this.summary = [];
        this.total = 0;
      }

      this.isLoading = false;
    },

    // Handle date range change from custom date picker
    handleDateChange() {
      // Validate date range - start date should not be after end date
      const startDate = new Date(this.startYear, parseInt(this.startMonth) - 1);
      const endDate = new Date(this.endYear, parseInt(this.endMonth) - 1);

      if (startDate > endDate) {
        // If start date is after end date, adjust end date to match start date
        this.endYear = this.startYear;
        this.endMonth = this.startMonth;
      }

      // Format as yyyy/mm for API
      this.params.start = `${this.startYear}/${this.startMonth}`;
      this.params.end = `${this.endYear}/${this.endMonth}`;
      this.params.timestamp = Date.now();

      console.log('Date range changed:', this.params.start, 'to', this.params.end);
      this.fetchVirtualBetsSummary();
    },

    // Clear date range and reset to current month
    clearDateRange() {
      const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
      const currentYear = new Date().getFullYear() >= 2025 ? new Date().getFullYear() : 2025;

      this.startMonth = currentMonth;
      this.startYear = currentYear;
      this.endMonth = currentMonth;
      this.endYear = currentYear;

      this.handleDateChange();
    },

    applyFilters() {

      if (this.filters.provider_name.key) {
        this.params.provider_name = this.filters.provider_name.key;
      } else {
        delete this.params.provider_name;
      }

      // Update timestamp for cache busting
      this.params.timestamp = Date.now();

      // Fetch data with new filters
      this.fetchVirtualBetsSummary();
    },

    // Debounced version of applyFilters for search input
    debouncedApplyFilters() {
      // Clear existing timeout
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      // Set new timeout for 500ms delay
      this.searchTimeout = setTimeout(() => {
        this.applyFilters();
      }, 500);
    },

    // Handle page change
    handlePageChange(page) {
      this.offset = page;
    },

    // Format date
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

  }
}
</script>

<style scoped>
.filter-card {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Badge container to ensure consistent alignment */
.badge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
}

/* Base badge style (standardized from master) */
.badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  letter-spacing: 0.3px;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Amount badges (standardized from master) */
.amount-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
  font-weight: 700;
  min-width: 120px;
}

.provider-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
}

/* Provider-specific styles (standardized from master) */
.provider-genius {
  background-color: #8E44AD;
  color: white;
}

.provider-betradar {
  background-color: #3498DB;
  color: white;
}

.provider-betgames {
  background-color: #27AE60;
  color: white;
}

.provider-evolution {
  background-color: #E74C3C;
  color: white;
}

.provider-pragmatic {
  background-color: #F39C12;
  color: white;
}

.provider-sporty {
  background-color: #9B59B6;
  color: white;
}

.provider-kiron {
  background-color: #E91E63;
  color: white;
}

.provider-ezugi {
  background-color: #D35400;
  color: white;
}

.provider-goldenrace {
  background-color: #8E44AD;
  color: white;
}

.provider-playtech {
  background-color: #16A085;
  color: white;
}

.provider-microgaming {
  background-color: #2980B9;
  color: white;
}

.provider-netent {
  background-color: #C0392B;
  color: white;
}

.provider-quickspin {
  background-color: #27AE60;
  color: white;
}

.provider-spinomenal {
  background-color: #7D3C98;
  color: white;
}

.provider-default {
  background-color: #2c6dff;
  color: white;
}

.responsive-datepicker {
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .responsive-datepicker {
    font-size: 0.875rem;
  }
}
</style>
