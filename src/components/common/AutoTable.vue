<template>
  <div class="auto-table-container bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span class="text-gray-600 text-sm">Loading...</span>
      </div>
    </div>

    <!-- Table -->
    <div v-else class="overflow-x-auto">
      <table class="auto-table w-full min-w-full">
      <thead class="border-b-2 text-xs text-left">
        <tr>
          <th
            v-for="header in tableHeaders"
            :key="header.key"
            :class="['py-2 px-2', headerStyles[header.key] || '']"
          >
            {{ header.label }}
          </th>
          <th v-if="hasActions" class="py-2 px-2 text-center">Actions</th>
        </tr>
      </thead>
      <tbody class="text-xs text-gray-600 divide-y">
        <tr v-if="tableData.length === 0">
          <td :colspan="hasActions ? tableHeaders.length + 1 : tableHeaders.length" class="py-4 text-center text-gray-500">
            {{ emptyMessage }}
          </td>
        </tr>
        <tr v-for="(row, rowIndex) in tableData" :key="rowIndex">
          <td
            v-for="header in tableHeaders"
            :key="`${rowIndex}-${header.key}`"
            :class="['py-2 px-2', columnStyles[header.key] || '']"
          >
            <slot :name="header.key" :item="row" :index="rowIndex">
              <template v-if="isLink(row[header.key])">
                <a :href="row[header.key]" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">
                  Link
                </a>
              </template>
              <template v-else>
                {{ formatCellValue(row[header.key], header.key) }}
              </template>
            </slot>
          </td>
          <td v-if="hasActions" class="py-2 px-2 text-center">
            <slot name="actions" :item="row" :index="rowIndex">
              <!-- Default actions dropdown if getActions function is provided -->
              <div v-if="getActions" class="relative inline-block">
                <!-- Actions dropdown button with better icon -->
                <button
                  @click="toggleActionDropdown(rowIndex)"
                  :data-row-index="rowIndex"
                  class="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-white border border-gray-300 rounded-full shadow-sm hover:bg-gray-50 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200"
                  :title="'Actions'"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                  </svg>
                </button>

                <!-- Dropdown Menu -->
                <div
                  v-if="activeDropdown === rowIndex"
                  class="fixed mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden max-h-64 overflow-y-auto"
                  :style="getDropdownPosition(rowIndex)"
                  style="z-index: 999999 !important; position: fixed !important;"
                  @click.stop
                >
                  <div class="py-2">
                    <button
                      v-for="(action, actionIndex) in getActions(row)"
                      :key="actionIndex"
                      @click="executeAction(action)"
                      :class="[
                        'w-full text-left px-4 py-3 text-sm hover:bg-gray-50 flex items-center transition-all duration-150 group',
                        getActionTextColor(action)
                      ]"
                    >
                      <div v-if="action.icon" class="flex-shrink-0 w-5 h-5 mr-3 flex items-center justify-center">
                        <i :class="[action.icon, getActionIconClass(action)]" class="text-sm"></i>
                      </div>
                      <span class="font-medium">{{ action.label }}</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Fallback if no getActions -->
              <div v-else-if="hasActions" class="text-gray-400 text-xs">
                No actions
              </div>
            </slot>
          </td>
        </tr>
      </tbody>
      </table>
    </div>

    <!-- Beautiful Pagination -->
    <div v-if="pagination && !loading && tableData.length > 0" class="bg-white border-t border-gray-200 px-6 py-4">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">

        <!-- Items count and per-page selector -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <!-- Items count -->
          <div v-if="showItemsCount" class="text-sm text-gray-700">
            Showing
            <span class="font-medium">{{ ((currentPageProp - 1) * itemsPerPage) + 1 }}</span>
            to
            <span class="font-medium">{{ Math.min(currentPageProp * itemsPerPage, totalItems) }}</span>
            of
            <span class="font-medium">{{ totalItems }}</span>
            results
          </div>

          <!-- Items per page selector -->
          <div class="flex items-center gap-2">
            <label for="items-per-page" class="text-sm text-gray-700 whitespace-nowrap">Show:</label>
            <select
              id="items-per-page"
              :value="itemsPerPage"
              @change="$emit('items-per-page-change', parseInt($event.target.value))"
              class="px-3 py-1.5 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
            <span class="text-sm text-gray-700">per page</span>
          </div>
        </div>

        <!-- Page navigation -->
        <div class="flex items-center gap-1">
          <!-- Previous button -->
          <button
            @click="$emit('page-change', currentPageProp - 1)"
            :disabled="currentPageProp <= 1"
            class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500 transition-colors"
          >
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            Previous
          </button>

          <!-- Page numbers -->
          <template v-for="page in visiblePages" :key="page">
            <span
              v-if="page === '...'"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300"
            >
              ...
            </span>
            <button
              v-else
              @click="$emit('page-change', page)"
              :class="[
                'inline-flex items-center px-3 py-2 text-sm font-medium border-t border-b border-gray-300 transition-colors',
                currentPageProp === page
                  ? 'bg-blue-50 text-blue-600 border-blue-500 z-10'
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              {{ page }}
            </button>
          </template>

          <!-- Next button -->
          <button
            @click="$emit('page-change', currentPageProp + 1)"
            :disabled="currentPageProp >= totalPages"
            class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500 transition-colors"
          >
            Next
            <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ActionDropdown from './ActionDropdown.vue';
import ActionItem from './ActionItem.vue';

export default {
  name: 'AutoTable',
  components: {
    ActionDropdown,
    ActionItem
  },
  props: {
    /**
     * The data to display in the table
     */
    data: {
      type: Array,
      required: false,
      default: () => []
    },
    /**
     * Optional array of column keys to include (in order)
     * If not provided, all columns from the first data item will be used
     */
    columns: {
      type: Array,
      default: null
    },
    /**
     * Optional array of column keys to exclude
     */
    excludeColumns: {
      type: Array,
      default: () => []
    },
    /**
     * Message to display when there is no data
     */
    emptyMessage: {
      type: String,
      default: 'No data available'
    },
    /**
     * Custom column name formatters
     * Example: { user_id: 'User ID', created_at: 'Created Date' }
     */
    columnFormatters: {
      type: Object,
      default: () => ({})
    },
    /**
     * Custom cell formatters by column
     * Example: { created_at: (value) => new Date(value).toLocaleDateString() }
     */
    cellFormatters: {
      type: Object,
      default: () => ({})
    },

    // DataTable-like properties
    /**
     * Array of header objects with key, label, and optional properties
     * Example: [{ key: 'name', label: 'Name', sortable: true }]
     */
    headers: {
      type: Array,
      default: null
    },
    /**
     * Alternative name for data prop (for DataTable compatibility)
     */
    items: {
      type: Array,
      default: null
    },
    /**
     * Show loading state
     */
    loading: {
      type: Boolean,
      default: false
    },
    /**
     * Show actions column
     */
    hasActions: {
      type: Boolean,
      default: false
    },
    /**
     * Total number of items (for pagination)
     */
    totalItems: {
      type: Number,
      default: 0
    },
    /**
     * Items per page
     */
    itemsPerPage: {
      type: Number,
      default: 100
    },
    /**
     * Current page
     */
    currentPageProp: {
      type: Number,
      default: 1
    },
    /**
     * Enable server-side pagination
     */
    serverSidePagination: {
      type: Boolean,
      default: false
    },
    /**
     * Show pagination
     */
    pagination: {
      type: Boolean,
      default: true
    },
    /**
     * Show items count
     */
    showItemsCount: {
      type: Boolean,
      default: true
    },

    // Optional formatting options
    /**
     * Date format options for specific columns
     * Example: { created_at: 'date', updated_at: 'datetime' }
     * Options: 'date' (YYYY-MM-DD), 'datetime' (full), 'time' (HH:MM:SS)
     */
    dateFormats: {
      type: Object,
      default: () => ({})
    },

    /**
     * Decimal places for numeric columns
     * Example: { price: 2, percentage: 1, amount: 3 }
     */
    decimalPlaces: {
      type: Object,
      default: () => ({})
    },

    /**
     * Disable automatic date formatting for specific columns
     * Example: ['raw_date_column', 'timestamp_field']
     */
    noAutoDateFormat: {
      type: Array,
      default: () => []
    },

    /**
     * Function that returns actions for each row
     * Example: (item) => [
     *   { label: 'Edit', action: () => editItem(item), icon: 'edit', class: 'text-blue-600' },
     *   { label: 'Delete', action: () => deleteItem(item), icon: 'delete', class: 'text-red-600' }
     * ]
     */
    getActions: {
      type: Function,
      default: null
    },

    /**
     * Custom CSS classes for specific columns (body cells)
     * Example: {
     *   amount: 'font-bold text-green-600 text-right',
     *   status: 'text-center font-semibold',
     *   priority: 'text-red-500 uppercase'
     * }
     */
    columnStyles: {
      type: Object,
      default: () => ({})
    },

    /**
     * Custom CSS classes for specific column headers
     * Example: {
     *   amount: 'text-right font-bold text-blue-600',
     *   status: 'text-center bg-gray-100',
     *   priority: 'text-red-600 uppercase'
     * }
     */
    headerStyles: {
      type: Object,
      default: () => ({})
    },

    /**
     * Options for items per page dropdown
     * Example: [10, 25, 50, 100]
     */
    itemsPerPageOptions: {
      type: Array,
      default: () => [10, 25, 50, 100]
    }
  },
  data() {
    return {
      activeDropdown: null,
      dropdownPositions: {}
    };
  },
  computed: {
    /**
     * Get the actual data to display (supports both data and items props)
     */
    tableData() {
      return this.items || this.data;
    },

    /**
     * Get the columns to display
     * If headers prop is provided (DataTable style), use it
     * If columns prop is provided, use it
     * Otherwise, get columns from the first data item
     */
    tableColumns() {
      if (this.headers) {
        return this.headers.map(header => header.key);
      }

      if (this.columns) {
        return this.columns;
      }

      if (this.tableData.length === 0) {
        return [];
      }

      // Get all keys from the first data item
      const allColumns = Object.keys(this.tableData[0]);

      // Filter out excluded columns
      return allColumns.filter(column => !this.excludeColumns.includes(column));
    },

    /**
     * Get column headers with labels
     */
    tableHeaders() {
      if (this.headers) {
        return this.headers;
      }

      // Create header objects from column keys
      return this.tableColumns.map(column => ({
        key: column,
        label: this.formatColumnName(column)
      }));
    },

    /**
     * Calculate total pages
     */
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },

    /**
     * Calculate visible page numbers for pagination
     */
    visiblePages() {
      const total = this.totalPages;
      const current = this.currentPageProp;
      const pages = [];

      if (total <= 7) {
        // Show all pages if total is 7 or less
        for (let i = 1; i <= total; i++) {
          pages.push(i);
        }
      } else {
        // Always show first page
        pages.push(1);

        if (current <= 4) {
          // Show pages 2, 3, 4, 5, ..., last
          for (let i = 2; i <= 5; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(total);
        } else if (current >= total - 3) {
          // Show 1, ..., last-4, last-3, last-2, last-1, last
          pages.push('...');
          for (let i = total - 4; i <= total; i++) {
            pages.push(i);
          }
        } else {
          // Show 1, ..., current-1, current, current+1, ..., last
          pages.push('...');
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(total);
        }
      }

      return pages;
    }
  },
  methods: {
    /**
     * Format a column name for display
     * @param {string} name - The column name
     * @returns {string} - The formatted column name
     */
    formatColumnName(name) {
      // Always use the default formatting: replace underscores with spaces and capitalize each word
      return name
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    },

    /**
     * Format a cell value for display
     * @param {any} value - The cell value
     * @param {string} column - The column name
     * @returns {string} - The formatted cell value
     */
    formatCellValue(value, column) {
      // Handle null or undefined values
      if (value === null || value === undefined) {
        return '-';
      }

      // Apply custom cell formatters first (if provided)
      if (this.cellFormatters[column]) {
        return this.cellFormatters[column](value);
      }

      // Handle decimal places for numeric values
      if (this.decimalPlaces[column] !== undefined && !isNaN(value)) {
        const decimals = this.decimalPlaces[column];
        return parseFloat(value).toFixed(decimals);
      }

      // Handle date formatting (only if not in noAutoDateFormat list)
      if (!this.noAutoDateFormat.includes(column)) {
        if (typeof value === 'string' &&
            (value.match(/^\d{4}-\d{2}-\d{2}/) ||
             value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/))) {
          try {
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
              // Check for custom date format
              const dateFormat = this.dateFormats[column];

              if (dateFormat === 'date') {
                // Return only date part (YYYY-MM-DD)
                return date.toISOString().split('T')[0];
              } else if (dateFormat === 'time') {
                // Return only time part (HH:MM:SS)
                return date.toTimeString().split(' ')[0];
              } else if (dateFormat === 'datetime') {
                // Return full datetime
                return date.toLocaleString();
              } else {
                // Default: return full datetime
                return date.toLocaleString();
              }
            }
          } catch (e) {
            // If date parsing fails, return the original value
          }
        }
      }

      // Format boolean values
      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
      }

      // Format numeric status values (common in APIs)
      if (column === 'status' && (value === '0' || value === '1' || value === 0 || value === 1)) {
        return value === '1' || value === 1 ? 'Active' : 'Inactive';
      }

      return value;
    },

    /**
     * Check if a value is a URL
     * @param {any} value - The value to check
     * @returns {boolean} - True if the value is a URL
     */
    isLink(value) {
      if (typeof value !== 'string') {
        return false;
      }

      return value.startsWith('http://') || value.startsWith('https://');
    },

    /**
     * Execute an action
     * @param {Object} action - The action object
     */
    executeAction(action) {
      if (action.action && typeof action.action === 'function') {
        action.action();
      }
      // Close the dropdown after executing action
      this.activeDropdown = null;
    },

    /**
     * Get action color based on action properties
     * @param {Object} action - The action object
     * @returns {string} - The color class
     */
    getActionColor(action) {
      // If action has a specific color, use it
      if (action.color) {
        return action.color;
      }

      // Auto-detect color based on action label or icon
      const label = action.label?.toLowerCase() || '';

      if (label.includes('edit') || label.includes('update') || label.includes('modify')) {
        return 'blue';
      } else if (label.includes('delete') || label.includes('remove') || label.includes('ban')) {
        return 'red';
      } else if (label.includes('view') || label.includes('show') || label.includes('details')) {
        return 'indigo';
      } else if (label.includes('activate') || label.includes('enable') || label.includes('approve')) {
        return 'green';
      } else if (label.includes('deactivate') || label.includes('disable') || label.includes('suspend')) {
        return 'yellow';
      }

      return 'default';
    },

    /**
     * Get action icon color class
     * @param {Object} action - The action object
     * @returns {string} - The icon color class
     */
    getActionIconClass(action) {
      const color = this.getActionColor(action);

      const colorMap = {
        'blue': 'text-blue-500 group-hover:text-blue-600',
        'red': 'text-red-500 group-hover:text-red-600',
        'green': 'text-green-500 group-hover:text-green-600',
        'yellow': 'text-yellow-500 group-hover:text-yellow-600',
        'indigo': 'text-indigo-500 group-hover:text-indigo-600',
        'default': 'text-gray-500 group-hover:text-gray-600'
      };

      return colorMap[color] || colorMap['default'];
    },

    /**
     * Get action text color class
     * @param {Object} action - The action object
     * @returns {string} - The text color class
     */
    getActionTextColor(action) {
      const color = this.getActionColor(action);

      const colorMap = {
        'blue': 'text-blue-700 hover:text-blue-800',
        'red': 'text-red-700 hover:text-red-800',
        'green': 'text-green-700 hover:text-green-800',
        'yellow': 'text-yellow-700 hover:text-yellow-800',
        'indigo': 'text-indigo-700 hover:text-indigo-800',
        'default': 'text-gray-700 hover:text-gray-800'
      };

      return colorMap[color] || colorMap['default'];
    },

    /**
     * Toggle action dropdown for a specific row
     * @param {number} rowIndex - The index of the row
     */
    toggleActionDropdown(rowIndex) {
      if (this.activeDropdown === rowIndex) {
        this.activeDropdown = null;
        // Remove dropdown-open class
        this.$el.classList.remove('dropdown-open');
      } else {
        this.activeDropdown = rowIndex;
        // Add dropdown-open class to prevent clipping
        this.$el.classList.add('dropdown-open');
        // Calculate position for the dropdown
        this.$nextTick(() => {
          this.calculateDropdownPosition(rowIndex);
        });
      }
    },

    /**
     * Calculate dropdown position to ensure it's visible
     * @param {number} rowIndex - The index of the row
     */
    calculateDropdownPosition(rowIndex) {
      const button = document.querySelector(`[data-row-index="${rowIndex}"]`);
      if (button) {
        const rect = button.getBoundingClientRect();
        const dropdownWidth = 224; // w-56 = 14rem = 224px
        const dropdownHeight = 250; // Increased to account for more actions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Find pagination section to avoid overlap
        const paginationSection = this.$el.querySelector('.pagination-section, .flex.justify-between, .flex.items-center.justify-between, [class*="pagination"]');
        let paginationTop = viewportHeight;
        if (paginationSection) {
          const paginationRect = paginationSection.getBoundingClientRect();
          paginationTop = paginationRect.top - 10; // Add 10px buffer
        }

        // Also check for any parent containers that might clip the dropdown
        const tableContainer = this.$el.closest('.overflow-hidden, .overflow-x-auto, .overflow-y-auto');
        if (tableContainer) {
          const containerRect = tableContainer.getBoundingClientRect();
          paginationTop = Math.min(paginationTop, containerRect.bottom - 10);
        }

        // Calculate initial position (prefer right-aligned dropdown)
        let left = rect.right - dropdownWidth;
        let top = rect.bottom + 8;

        // Adjust horizontal position if dropdown would go off screen
        if (left < 10) {
          // If right-aligned goes off left edge, align to left of button
          left = rect.left;
        }
        if (left + dropdownWidth > viewportWidth - 10) {
          // If still goes off right edge, align to right edge of viewport
          left = viewportWidth - dropdownWidth - 10;
        }

        // Adjust vertical position considering pagination section
        const spaceBelow = Math.min(viewportHeight, paginationTop) - rect.bottom - 20; // 20px buffer
        const spaceAbove = rect.top - 20; // 20px buffer from top

        if (spaceBelow >= dropdownHeight) {
          // Enough space below, show below button
          top = rect.bottom + 8;
        } else if (spaceAbove >= dropdownHeight) {
          // Not enough space below but enough above, show above button
          top = rect.top - dropdownHeight - 8;
        } else {
          // Limited space both above and below - prioritize showing above for last rows
          const isLastRow = rowIndex >= this.tableData.length - 3; // Consider last 3 rows

          if (isLastRow || spaceAbove > spaceBelow) {
            // Show above for last rows or when more space above
            top = Math.max(20, rect.top - dropdownHeight - 8);
          } else {
            // Show below but limit height
            top = rect.bottom + 8;
          }
        }

        // Ensure dropdown doesn't go above viewport or below pagination
        // For last rows, prioritize staying above pagination even if it means going higher
        if (rowIndex >= this.tableData.length - 2) {
          top = Math.max(10, Math.min(top, paginationTop - dropdownHeight - 20));
        } else {
          top = Math.max(10, Math.min(top, paginationTop - dropdownHeight - 10));
        }

        this.dropdownPositions[rowIndex] = {
          left: `${Math.max(10, left)}px`,
          top: `${Math.max(10, top)}px`
        };
      }
    },

    /**
     * Get dropdown position for a specific row
     * @param {number} rowIndex - The index of the row
     */
    getDropdownPosition(rowIndex) {
      return this.dropdownPositions[rowIndex] || { left: '0px', top: '0px' };
    },

    /**
     * Close dropdown when clicking outside
     */
    closeDropdown() {
      this.activeDropdown = null;
      // Remove dropdown-open class
      if (this.$el) {
        this.$el.classList.remove('dropdown-open');
      }
    },

    /**
     * Handle window resize to recalculate dropdown position
     */
    handleWindowResize() {
      if (this.activeDropdown !== null) {
        this.$nextTick(() => {
          this.calculateDropdownPosition(this.activeDropdown);
        });
      }
    },

    /**
     * Handle scroll events to close dropdown or recalculate position
     */
    handleScroll() {
      if (this.activeDropdown !== null) {
        // Close dropdown on scroll to prevent positioning issues
        this.closeDropdown();
      }
    }
  },

  mounted() {
    // Close dropdown when clicking outside
    document.addEventListener('click', this.closeDropdown);
    // Recalculate dropdown position on window resize
    window.addEventListener('resize', this.handleWindowResize);
    // Close dropdown on scroll to prevent positioning issues
    window.addEventListener('scroll', this.handleScroll, true);
  },

  beforeUnmount() {
    // Clean up event listeners
    document.removeEventListener('click', this.closeDropdown);
    window.removeEventListener('resize', this.handleWindowResize);
    window.removeEventListener('scroll', this.handleScroll, true);
  }
}
</script>

<style scoped>
.auto-table-container {
  -webkit-overflow-scrolling: touch;
  position: relative;
}

/* Ensure dropdowns are not clipped by table containers */
.auto-table-container:has(.fixed[style*="z-index: 999999"]) {
  overflow: visible !important;
}

/* Alternative approach for browsers that don't support :has() */
.auto-table-container.dropdown-open {
  overflow: visible !important;
}

/* Add padding at bottom to prevent dropdown clipping */
.auto-table-container {
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.auto-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
}

.auto-table th {
  font-weight: 600;
  white-space: nowrap;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

/* Default left alignment for headers without custom styles */
.auto-table th:not([class*="text-"]) {
  text-align: left;
}

.auto-table th, .auto-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.auto-table tbody tr {
  transition: background-color 0.15s ease-in-out;
}

.auto-table tbody tr:hover {
  background-color: #f9fafb;
}

.auto-table tbody tr:last-child td {
  border-bottom: none;
}

/* Action dropdown styles */
.auto-table .relative {
  position: relative;
}

.auto-table .absolute {
  position: absolute;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

/* Ensure dropdown appears above other content */
.auto-table td {
  position: relative;
}

/* Enhanced dropdown positioning */
.auto-table .fixed {
  position: fixed !important;
  z-index: 999999 !important;
  animation: fadeIn 0.2s ease-out;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  pointer-events: auto;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Dropdown scrolling styles */
.auto-table .fixed.max-h-64 {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.auto-table .fixed.max-h-64::-webkit-scrollbar {
  width: 6px;
}

.auto-table .fixed.max-h-64::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.auto-table .fixed.max-h-64::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.auto-table .fixed.max-h-64::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Action button hover effects */
.auto-table button:hover svg {
  transform: scale(1.05);
}

/* Dropdown animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Pagination styles */
.auto-table-container .bg-blue-50 {
  position: relative;
}

.auto-table-container .bg-blue-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  z-index: -1;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .auto-table {
    font-size: 0.875rem;
    min-width: 500px;
  }

  .auto-table th, .auto-table td {
    padding: 0.5rem 0.75rem;
  }

  /* Adjust dropdown width on mobile */
  .auto-table .absolute {
    width: 48vw;
    min-width: 180px;
    left: auto;
    right: 0;
  }

  /* Stack pagination on mobile */
  .auto-table-container .flex-col {
    align-items: stretch;
  }

  .auto-table-container .gap-4 {
    gap: 1rem;
  }

  /* Smaller pagination buttons on mobile */
  .auto-table-container button {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .auto-table th, .auto-table td {
    padding: 0.5rem;
  }

  .auto-table {
    font-size: 0.8rem;
  }

  /* Full width dropdown on small screens */
  .auto-table .absolute {
    width: 90vw;
    max-width: 200px;
  }
}
</style>
